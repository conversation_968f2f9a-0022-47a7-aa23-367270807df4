import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../providers/character_provider.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';
import '../widgets/animated_background.dart';
import '../widgets/character_selection_grid.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _contentController;
  bool _showCharacterSelection = false;

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: AppConstants.backgroundAnimationDuration,
      vsync: this,
    );
    _contentController = AnimationController(
      duration: AppConstants.longAnimationDuration,
      vsync: this,
    );

    _startAnimations();
  }

  void _startAnimations() async {
    // بدء حركة الخلفية
    _backgroundController.repeat();

    // انتظار قليل ثم بدء حركة المحتوى
    await Future.delayed(const Duration(milliseconds: 500));
    _contentController.forward();

    // إظهار اختيار الشخصيات بعد انتهاء الترحيب
    await Future.delayed(const Duration(seconds: 3));
    if (mounted) {
      setState(() {
        _showCharacterSelection = true;
      });
    }
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // الخلفية المتحركة
          AnimatedBackground(controller: _backgroundController),

          // المحتوى الرئيسي
          SafeArea(
            child: AnimatedSwitcher(
              duration: AppConstants.longAnimationDuration,
              child:
                  _showCharacterSelection
                      ? _buildCharacterSelectionView()
                      : _buildWelcomeView(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeView() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          // تأثيرات ضوئية إضافية
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _contentController,
              builder: (context, child) {
                return Container(
                  decoration: BoxDecoration(
                    gradient: RadialGradient(
                      center: Alignment.center,
                      radius: 1.5,
                      colors: [
                        AppColors.primary.withValues(alpha: 0.1),
                        Colors.transparent,
                        AppColors.secondary.withValues(alpha: 0.05),
                      ],
                      stops: [0.0, 0.7, 1.0],
                    ),
                  ),
                );
              },
            ),
          ),

          // المحتوى الرئيسي
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // شعار التطبيق المحسن
                Container(
                      width: 180,
                      height: 180,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // الحلقة الخارجية المتحركة
                          AnimatedBuilder(
                            animation: _backgroundController,
                            builder: (context, child) {
                              return Transform.rotate(
                                angle:
                                    _backgroundController.value * 2 * 3.14159,
                                child: Container(
                                  width: 180,
                                  height: 180,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: AppColors.primary.withValues(
                                        alpha: 0.3,
                                      ),
                                      width: 2,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),

                          // الحلقة الوسطى
                          AnimatedBuilder(
                            animation: _backgroundController,
                            builder: (context, child) {
                              return Transform.rotate(
                                angle:
                                    -_backgroundController.value *
                                    1.5 *
                                    3.14159,
                                child: Container(
                                  width: 150,
                                  height: 150,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: AppColors.secondary.withValues(
                                        alpha: 0.4,
                                      ),
                                      width: 1.5,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),

                          // الشعار الأساسي
                          Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppColors.primary,
                                  AppColors.secondary,
                                  AppColors.primary.withValues(alpha: 0.8),
                                ],
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.primary.withValues(
                                    alpha: 0.6,
                                  ),
                                  blurRadius: 30,
                                  spreadRadius: 8,
                                  offset: const Offset(0, 10),
                                ),
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  blurRadius: 20,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.psychology_outlined,
                              size: 60,
                              color: Colors.white,
                              shadows: [
                                Shadow(
                                  color: Colors.black38,
                                  offset: Offset(0, 3),
                                  blurRadius: 6,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                    .animate(controller: _contentController)
                    .scale(
                      begin: const Offset(0.3, 0.3),
                      end: const Offset(1.0, 1.0),
                      duration: 1200.ms,
                      curve: Curves.elasticOut,
                    )
                    .fadeIn(duration: 800.ms),

                const SizedBox(height: 50),

                // عنوان التطبيق الرئيسي
                Container(
                      padding: const EdgeInsets.symmetric(horizontal: 30),
                      child: Text(
                        'العقل المبدع',
                        style: TextStyle(
                          fontSize: 36,
                          fontWeight: FontWeight.w900,
                          color: Colors.white,
                          letterSpacing: 2,
                          shadows: [
                            Shadow(
                              color: AppColors.primary.withValues(alpha: 0.5),
                              offset: const Offset(0, 3),
                              blurRadius: 8,
                            ),
                            Shadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              offset: const Offset(0, 2),
                              blurRadius: 4,
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )
                    .animate(controller: _contentController)
                    .slideY(
                      begin: 80,
                      end: 0,
                      duration: 1000.ms,
                      curve: Curves.easeOutCubic,
                    )
                    .fadeIn(delay: 400.ms, duration: 800.ms),

                const SizedBox(height: 20),

                // العنوان الفرعي المحسن
                Container(
                      padding: const EdgeInsets.symmetric(horizontal: 40),
                      child: Text(
                        'رفيقك الذكي في رحلة الإبداع والتعلم',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: Colors.white.withValues(alpha: 0.9),
                          height: 1.5,
                          shadows: [
                            Shadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              offset: const Offset(0, 1),
                              blurRadius: 3,
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )
                    .animate(controller: _contentController)
                    .slideY(
                      begin: 50,
                      end: 0,
                      duration: 1000.ms,
                      curve: Curves.easeOutCubic,
                    )
                    .fadeIn(delay: 700.ms, duration: 800.ms),

                const SizedBox(height: 80),

                // مؤشر التحميل المحسن
                Container(
                      width: 250,
                      height: 6,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(3),
                        color: Colors.white.withValues(alpha: 0.1),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: AnimatedBuilder(
                        animation: _contentController,
                        builder: (context, child) {
                          return Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(3),
                            ),
                            child: LinearProgressIndicator(
                              value: _contentController.value,
                              backgroundColor: Colors.transparent,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.secondary,
                              ),
                              borderRadius: BorderRadius.circular(3),
                            ),
                          );
                        },
                      ),
                    )
                    .animate(controller: _contentController)
                    .fadeIn(delay: 1000.ms, duration: 600.ms)
                    .scale(delay: 1000.ms, duration: 600.ms),

                const SizedBox(height: 30),

                // نص التحميل
                Text(
                      'جاري التحضير...',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.7),
                        fontWeight: FontWeight.w400,
                      ),
                    )
                    .animate(controller: _contentController)
                    .fadeIn(delay: 1200.ms, duration: 600.ms),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCharacterSelectionView() {
    return Column(
      children: [
        // العنوان
        Padding(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Column(
                children: [
                  Text(
                    AppConstants.characterSelectionTitle,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                      shadows: [
                        Shadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          offset: const Offset(0, 2),
                          blurRadius: 4,
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 10),
                  Text(
                    AppConstants.characterSelectionSubtitle,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
            .animate()
            .slideY(begin: -50, duration: 600.ms, curve: Curves.easeOutCubic)
            .fadeIn(duration: 600.ms),

        // شبكة اختيار الشخصيات
        Expanded(
          child: CharacterSelectionGrid(
                onCharacterSelected: _onCharacterSelected,
              )
              .animate()
              .slideY(begin: 100, duration: 800.ms, curve: Curves.easeOutCubic)
              .fadeIn(delay: 300.ms, duration: 600.ms),
        ),
      ],
    );
  }

  void _onCharacterSelected(String characterId) async {
    final characterProvider = context.read<CharacterProvider>();
    await characterProvider.selectCharacterById(characterId);

    if (mounted) {
      // الانتقال إلى شاشة الدردشة
      Navigator.of(context).pushReplacementNamed('/chat');
    }
  }
}
