import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../providers/character_provider.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';
import '../widgets/animated_background.dart';
import '../widgets/character_selection_grid.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _contentController;
  bool _showCharacterSelection = false;

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: AppConstants.backgroundAnimationDuration,
      vsync: this,
    );
    _contentController = AnimationController(
      duration: AppConstants.longAnimationDuration,
      vsync: this,
    );

    _startAnimations();
  }

  void _startAnimations() async {
    // بدء حركة الخلفية
    _backgroundController.repeat();
    
    // انتظار قليل ثم بدء حركة المحتوى
    await Future.delayed(const Duration(milliseconds: 500));
    _contentController.forward();
    
    // إظهار اختيار الشخصيات بعد انتهاء الترحيب
    await Future.delayed(const Duration(seconds: 3));
    if (mounted) {
      setState(() {
        _showCharacterSelection = true;
      });
    }
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // الخلفية المتحركة
          AnimatedBackground(controller: _backgroundController),
          
          // المحتوى الرئيسي
          SafeArea(
            child: AnimatedSwitcher(
              duration: AppConstants.longAnimationDuration,
              child: _showCharacterSelection
                  ? _buildCharacterSelectionView()
                  : _buildWelcomeView(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // شعار التطبيق
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: AppColors.primaryGradient,
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: const Icon(
              Icons.psychology,
              size: 60,
              color: Colors.white,
            ),
          )
              .animate(controller: _contentController)
              .scale(
                begin: const Offset(0.5, 0.5),
                end: const Offset(1.0, 1.0),
                duration: 800.ms,
                curve: Curves.elasticOut,
              )
              .fadeIn(duration: 600.ms),

          const SizedBox(height: 40),

          // عنوان الترحيب
          Text(
            AppConstants.welcomeTitle,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 28,
                  shadows: [
                    Shadow(
                      color: Colors.black.withOpacity(0.3),
                      offset: const Offset(0, 2),
                      blurRadius: 4,
                    ),
                  ],
                ),
            textAlign: TextAlign.center,
          )
              .animate(controller: _contentController)
              .slideY(
                begin: 50,
                end: 0,
                duration: 800.ms,
                curve: Curves.easeOutCubic,
              )
              .fadeIn(delay: 300.ms, duration: 600.ms),

          const SizedBox(height: 20),

          // العنوان الفرعي
          Text(
            AppConstants.welcomeSubtitle,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 18,
                  shadows: [
                    Shadow(
                      color: Colors.black.withOpacity(0.2),
                      offset: const Offset(0, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
            textAlign: TextAlign.center,
          )
              .animate(controller: _contentController)
              .slideY(
                begin: 30,
                end: 0,
                duration: 800.ms,
                curve: Curves.easeOutCubic,
              )
              .fadeIn(delay: 600.ms, duration: 600.ms),

          const SizedBox(height: 60),

          // مؤشر التحميل
          Container(
            width: 200,
            height: 4,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              color: Colors.white.withOpacity(0.2),
            ),
            child: AnimatedBuilder(
              animation: _contentController,
              builder: (context, child) {
                return LinearProgressIndicator(
                  value: _contentController.value,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColors.secondary,
                  ),
                );
              },
            ),
          )
              .animate(controller: _contentController)
              .fadeIn(delay: 900.ms, duration: 400.ms)
              .scale(delay: 900.ms, duration: 400.ms),
        ],
      ),
    );
  }

  Widget _buildCharacterSelectionView() {
    return Column(
      children: [
        // العنوان
        Padding(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Column(
            children: [
              Text(
                AppConstants.characterSelectionTitle,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                      shadows: [
                        Shadow(
                          color: Colors.black.withOpacity(0.3),
                          offset: const Offset(0, 2),
                          blurRadius: 4,
                        ),
                      ],
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              Text(
                AppConstants.characterSelectionSubtitle,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 16,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        )
            .animate()
            .slideY(begin: -50, duration: 600.ms, curve: Curves.easeOutCubic)
            .fadeIn(duration: 600.ms),

        // شبكة اختيار الشخصيات
        Expanded(
          child: CharacterSelectionGrid(
            onCharacterSelected: _onCharacterSelected,
          )
              .animate()
              .slideY(begin: 100, duration: 800.ms, curve: Curves.easeOutCubic)
              .fadeIn(delay: 300.ms, duration: 600.ms),
        ),
      ],
    );
  }

  void _onCharacterSelected(String characterId) async {
    final characterProvider = context.read<CharacterProvider>();
    await characterProvider.selectCharacterById(characterId);
    
    if (mounted) {
      // الانتقال إلى شاشة الدردشة
      Navigator.of(context).pushReplacementNamed('/chat');
    }
  }
}
