class AppConstants {
  // App Information
  static const String appName = 'العقل المبدع';
  static const String appSubtitle = 'دردشة AI متعددة الشخصيات';
  static const String appVersion = '1.0.0';
  
  // Welcome Messages
  static const String welcomeTitle = 'مرحبًا بك في عالَم العقول الذكية...';
  static const String welcomeSubtitle = 'اختر رفيقك اليوم';
  static const String characterSelectionTitle = '🎭 اختر شخصيتك المفضلة 🎭';
  static const String characterSelectionSubtitle = 'كل شخصية لها طابعها الخاص وأسلوبها المميز';
  
  // Animation Durations
  static const Duration splashDuration = Duration(seconds: 4);
  static const Duration shortAnimationDuration = Duration(milliseconds: 300);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 500);
  static const Duration longAnimationDuration = Duration(milliseconds: 800);
  static const Duration typingIndicatorDuration = Duration(milliseconds: 1500);
  
  // UI Constants
  static const double borderRadius = 16.0;
  static const double cardElevation = 8.0;
  static const double buttonHeight = 48.0;
  static const double iconSize = 24.0;
  static const double avatarSize = 48.0;
  static const double characterCardSize = 200.0;
  
  // Padding and Margins
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // Chat Constants
  static const int maxMessageLength = 1000;
  static const int maxChatHistory = 50;
  static const int typingDotsCount = 3;
  static const double messageMaxWidth = 0.85;
  static const double chatInputMinHeight = 40.0;
  static const double chatInputMaxHeight = 120.0;
  
  // Database Constants
  static const String databaseName = 'al_aql_al_mobde.db';
  static const int databaseVersion = 1;
  static const String chatHistoryTable = 'chat_history';
  static const String userPreferencesTable = 'user_preferences';
  
  // Storage Keys
  static const String selectedCharacterKey = 'selected_character';
  static const String chatHistoryKey = 'chat_history';
  static const String userPreferencesKey = 'user_preferences';
  static const String firstLaunchKey = 'first_launch';
  static const String themeKey = 'theme_mode';
  
  // Character IDs (matching original application)
  static const String creativityCharacterId = 'creativity';
  static const String educationCharacterId = 'education';
  static const String communicationCharacterId = 'communication';
  static const String wellnessCharacterId = 'wellness';
  
  // Character Names (Arabic)
  static const String creativityCharacterName = 'فَتّان';
  static const String educationCharacterName = 'معلّم';
  static const String communicationCharacterName = 'المستشار';
  static const String wellnessCharacterName = 'هادئ';
  
  // Character Titles
  static const String creativityCharacterTitle = 'الفنان الملهم';
  static const String educationCharacterTitle = 'المعلم الحكيم';
  static const String communicationCharacterTitle = 'خبير العلاقات';
  static const String wellnessCharacterTitle = 'مرشدة السلام النفسي';
  
  // Character Emojis
  static const String creativityEmoji = '🎨';
  static const String educationEmoji = '📚';
  static const String communicationEmoji = '💼';
  static const String wellnessEmoji = '🧘‍♀️';
  
  // API Constants (for future AI integration)
  static const String apiBaseUrl = 'https://api.example.com';
  static const String chatEndpoint = '/chat';
  static const int apiTimeoutSeconds = 30;
  
  // Error Messages
  static const String networkErrorMessage = 'خطأ في الاتصال بالشبكة';
  static const String generalErrorMessage = 'حدث خطأ غير متوقع';
  static const String emptyMessageError = 'لا يمكن إرسال رسالة فارغة';
  static const String characterNotSelectedError = 'يرجى اختيار شخصية أولاً';
  
  // Success Messages
  static const String messageSentSuccess = 'تم إرسال الرسالة بنجاح';
  static const String chatSavedSuccess = 'تم حفظ المحادثة';
  
  // Placeholder Texts
  static const String messageInputPlaceholder = 'اكتب رسالتك هنا...';
  static const String searchPlaceholder = 'البحث...';
  static const String noMessagesPlaceholder = 'لا توجد رسائل بعد';
  static const String startChatPlaceholder = 'ابدأ محادثتك الأسطورية...';
  
  // Button Texts
  static const String sendButtonText = 'إرسال';
  static const String backButtonText = 'رجوع';
  static const String clearChatButtonText = 'مسح المحادثة';
  static const String settingsButtonText = 'الإعدادات';
  static const String aboutButtonText = 'حول التطبيق';
  
  // Regex Patterns
  static const String arabicTextPattern = r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]';
  static const String englishTextPattern = r'[a-zA-Z]';
  
  // File Paths
  static const String assetsPath = 'assets/';
  static const String imagesPath = 'assets/images/';
  static const String iconsPath = 'assets/icons/';
  static const String animationsPath = 'assets/animations/';
  static const String fontsPath = 'assets/fonts/';
  
  // Font Families
  static const String primaryFontFamily = 'Cairo';
  static const String secondaryFontFamily = 'Vazirmatn';
  
  // Text Styles
  static const double headingFontSize = 24.0;
  static const double titleFontSize = 20.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 14.0;
  static const double smallFontSize = 12.0;
  
  // Background Animation
  static const Duration backgroundAnimationDuration = Duration(seconds: 20);
  static const int particleCount = 50;
  static const double particleSize = 4.0;
  static const double particleOpacity = 0.7;
}
