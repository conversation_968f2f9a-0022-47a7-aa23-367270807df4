import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/ai_character.dart';
import '../utils/app_constants.dart';

class CharacterCard extends StatefulWidget {
  final AICharacter character;
  final bool isSelected;
  final VoidCallback onTap;

  const CharacterCard({
    super.key,
    required this.character,
    required this.isSelected,
    required this.onTap,
  });

  @override
  State<CharacterCard> createState() => _CharacterCardState();
}

class _CharacterCardState extends State<CharacterCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: AppConstants.shortAnimationDuration,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 400;
    final iconSize = isSmallScreen ? 70.0 : 90.0;
    final titleFontSize = isSmallScreen ? 22.0 : 26.0;
    final subtitleFontSize = isSmallScreen ? 14.0 : 16.0;

    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: (_) => _onHoverStart(),
      onTapUp: (_) => _onHoverEnd(),
      onTapCancel: _onHoverEnd,
      child: AnimatedContainer(
        duration: AppConstants.shortAnimationDuration,
        curve: Curves.easeInOut,
        transform: Matrix4.identity()
          ..scale(_isHovered ? 0.95 : 1.0)
          ..rotateZ(_isHovered ? 0.02 : 0.0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: widget.isSelected
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      widget.character.primaryColor,
                      widget.character.secondaryColor,
                    ],
                  )
                : LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      widget.character.primaryColor.withValues(alpha: 0.15),
                      widget.character.secondaryColor.withValues(alpha: 0.08),
                      Colors.black.withValues(alpha: 0.02),
                    ],
                  ),
            border: Border.all(
              color: widget.isSelected
                  ? Colors.white.withValues(alpha: 0.4)
                  : Colors.white.withValues(alpha: 0.1),
              width: widget.isSelected ? 2.5 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: widget.character.primaryColor.withValues(
                  alpha: widget.isSelected ? 0.4 : 0.15,
                ),
                blurRadius: widget.isSelected ? 25 : 15,
                spreadRadius: widget.isSelected ? 3 : 0,
                offset: const Offset(0, 8),
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            child: Stack(
              children: [
                // خلفية متحركة
                if (widget.isSelected) _buildAnimatedBackground(),

                // المحتوى الرئيسي
                Padding(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // الرمز التعبيري - تصميم حديث
                      Container(
                        width: iconSize,
                        height: iconSize,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: widget.isSelected
                              ? LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white.withValues(alpha: 0.3),
                                    Colors.white.withValues(alpha: 0.1),
                                  ],
                                )
                              : LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white.withValues(alpha: 0.15),
                                    Colors.white.withValues(alpha: 0.05),
                                  ],
                                ),
                          border: Border.all(
                            color: widget.isSelected
                                ? Colors.white.withValues(alpha: 0.5)
                                : Colors.white.withValues(alpha: 0.2),
                            width: widget.isSelected ? 2 : 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: widget.character.primaryColor.withValues(
                                alpha: widget.isSelected ? 0.3 : 0.1,
                              ),
                              blurRadius: widget.isSelected ? 15 : 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            widget.character.emoji,
                            style: TextStyle(
                              fontSize: iconSize * 0.45,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withValues(alpha: 0.2),
                                  offset: const Offset(0, 2),
                                  blurRadius: 4,
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                          .animate(target: widget.isSelected ? 1 : 0)
                          .scale(
                            begin: const Offset(1.0, 1.0),
                            end: const Offset(1.15, 1.15),
                            duration: 400.ms,
                            curve: Curves.elasticOut,
                          )
                          .shimmer(
                            duration: 2000.ms,
                            color: Colors.white.withValues(alpha: 0.4),
                          ),

                      const SizedBox(height: 12),

                      // اسم الشخصية
                      Text(
                        widget.character.name,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: titleFontSize,
                          shadows: [
                            Shadow(
                              color: Colors.black.withOpacity(0.3),
                              offset: const Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 6),

                      // لقب الشخصية
                      Text(
                        widget.character.title,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withOpacity(0.8),
                              fontSize: subtitleFontSize,
                            ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 12),

                      // مؤشر الاختيار
                      if (widget.isSelected)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            'مختارة',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 12,
                                    ),
                          ),
                        )
                            .animate()
                            .fadeIn(duration: 300.ms)
                            .scale(duration: 300.ms)
                            .shimmer(
                              duration: 2000.ms,
                              color: Colors.white.withOpacity(0.5),
                            ),
                    ],
                  ),
                ),

                // تأثير الضغط
                if (_isHovered)
                  Container(
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(AppConstants.borderRadius),
                      color: Colors.white.withOpacity(0.1),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              widget.character.primaryColor.withOpacity(0.1),
              widget.character.secondaryColor.withOpacity(0.1),
              widget.character.primaryColor.withOpacity(0.05),
            ],
          ),
        ),
      ).animate(onPlay: (controller) => controller.repeat()).shimmer(
            duration: 3000.ms,
            color: Colors.white.withOpacity(0.1),
          ),
    );
  }

  void _onHoverStart() {
    setState(() {
      _isHovered = true;
    });
    _hoverController.forward();
  }

  void _onHoverEnd() {
    setState(() {
      _isHovered = false;
    });
    _hoverController.reverse();
  }
}
