import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/ai_character.dart';
import '../utils/app_constants.dart';

class CharacterCard extends StatefulWidget {
  final AICharacter character;
  final bool isSelected;
  final VoidCallback onTap;

  const CharacterCard({
    super.key,
    required this.character,
    required this.isSelected,
    required this.onTap,
  });

  @override
  State<CharacterCard> createState() => _CharacterCardState();
}

class _CharacterCardState extends State<CharacterCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: AppConstants.shortAnimationDuration,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 400;
    final iconSize = isSmallScreen ? 80.0 : 100.0;
    final titleFontSize = isSmallScreen ? 24.0 : 28.0;
    final subtitleFontSize = isSmallScreen ? 15.0 : 17.0;

    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: (_) => _onHoverStart(),
      onTapUp: (_) => _onHoverEnd(),
      onTapCancel: _onHoverEnd,
      child: AnimatedContainer(
        duration: AppConstants.shortAnimationDuration,
        curve: Curves.easeInOut,
        transform: Matrix4.identity()
          ..scale(_isHovered ? 0.95 : 1.0)
          ..rotateZ(_isHovered ? 0.02 : 0.0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(28),
            gradient: widget.isSelected
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      widget.character.primaryColor.withValues(alpha: 0.95),
                      widget.character.primaryColor.withValues(alpha: 0.85),
                      widget.character.secondaryColor.withValues(alpha: 0.9),
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  )
                : LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFFF8F9FA),
                      const Color(0xFFF1F3F4),
                    ],
                  ),
            border: Border.all(
              color: widget.isSelected
                  ? Colors.white.withValues(alpha: 0.8)
                  : widget.character.primaryColor.withValues(alpha: 0.4),
              width: widget.isSelected ? 3.5 : 2.5,
            ),
            boxShadow: [
              // ظل رئيسي ملون
              BoxShadow(
                color: widget.character.primaryColor.withValues(
                  alpha: widget.isSelected ? 0.5 : 0.25,
                ),
                blurRadius: widget.isSelected ? 35 : 25,
                spreadRadius: widget.isSelected ? 6 : 3,
                offset: const Offset(0, 15),
              ),
              // ظل ثانوي للعمق
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.12),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
              // ظل داخلي للتأثير الزجاجي
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.6),
                blurRadius: 1,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            child: Stack(
              children: [
                // خلفية متحركة
                if (widget.isSelected) _buildAnimatedBackground(),

                // المحتوى الرئيسي
                Padding(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // الرمز التعبيري - تصميم فاخر ومتطور
                      Container(
                        width: iconSize + 20,
                        height: iconSize + 20,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(35),
                          gradient: widget.isSelected
                              ? LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white.withValues(alpha: 0.9),
                                    Colors.white.withValues(alpha: 0.7),
                                    Colors.white.withValues(alpha: 0.5),
                                  ],
                                  stops: const [0.0, 0.5, 1.0],
                                )
                              : LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    widget.character.primaryColor
                                        .withValues(alpha: 0.15),
                                    widget.character.primaryColor
                                        .withValues(alpha: 0.08),
                                    widget.character.secondaryColor
                                        .withValues(alpha: 0.05),
                                  ],
                                ),
                          border: Border.all(
                            color: widget.isSelected
                                ? Colors.white.withValues(alpha: 0.9)
                                : widget.character.primaryColor
                                    .withValues(alpha: 0.3),
                            width: widget.isSelected ? 4 : 3,
                          ),
                          boxShadow: [
                            // ظل ملون قوي
                            BoxShadow(
                              color: widget.character.primaryColor.withValues(
                                alpha: widget.isSelected ? 0.6 : 0.3,
                              ),
                              blurRadius: widget.isSelected ? 25 : 15,
                              spreadRadius: widget.isSelected ? 3 : 1,
                              offset: const Offset(0, 8),
                            ),
                            // ظل أسود للعمق
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.15),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                            // إضاءة داخلية
                            BoxShadow(
                              color: Colors.white.withValues(alpha: 0.8),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            widget.character.emoji,
                            style: TextStyle(
                              fontSize: iconSize * 0.6,
                              fontWeight: FontWeight.w700,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withValues(alpha: 0.2),
                                  offset: const Offset(0, 3),
                                  blurRadius: 6,
                                ),
                                Shadow(
                                  color: widget.character.primaryColor
                                      .withValues(alpha: 0.3),
                                  offset: const Offset(0, 1),
                                  blurRadius: 2,
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                          .animate(target: widget.isSelected ? 1 : 0)
                          .scale(
                            begin: const Offset(1.0, 1.0),
                            end: const Offset(1.15, 1.15),
                            duration: 400.ms,
                            curve: Curves.elasticOut,
                          )
                          .shimmer(
                            duration: 2000.ms,
                            color: Colors.white.withValues(alpha: 0.4),
                          ),

                      const SizedBox(height: 12),

                      // اسم الشخصية - تصميم فاخر
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          gradient: widget.isSelected
                              ? LinearGradient(
                                  colors: [
                                    Colors.white.withValues(alpha: 0.3),
                                    Colors.white.withValues(alpha: 0.1),
                                  ],
                                )
                              : null,
                        ),
                        child: Text(
                          widget.character.name,
                          style: TextStyle(
                            color: widget.isSelected
                                ? Colors.white
                                : widget.character.primaryColor,
                            fontWeight: FontWeight.w900,
                            fontSize: titleFontSize + 4,
                            letterSpacing: 1.2,
                            height: 1.2,
                            shadows: widget.isSelected
                                ? [
                                    Shadow(
                                      color:
                                          Colors.black.withValues(alpha: 0.5),
                                      offset: const Offset(0, 3),
                                      blurRadius: 6,
                                    ),
                                    Shadow(
                                      color: widget.character.primaryColor
                                          .withValues(alpha: 0.4),
                                      offset: const Offset(0, 1),
                                      blurRadius: 2,
                                    ),
                                  ]
                                : [
                                    Shadow(
                                      color: widget.character.primaryColor
                                          .withValues(alpha: 0.4),
                                      offset: const Offset(0, 2),
                                      blurRadius: 4,
                                    ),
                                    Shadow(
                                      color:
                                          Colors.white.withValues(alpha: 0.8),
                                      offset: const Offset(0, 1),
                                      blurRadius: 1,
                                    ),
                                  ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 12),

                      // لقب الشخصية - تصميم أنيق ومتطور
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          gradient: widget.isSelected
                              ? LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white.withValues(alpha: 0.25),
                                    Colors.white.withValues(alpha: 0.15),
                                    Colors.white.withValues(alpha: 0.05),
                                  ],
                                )
                              : LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    widget.character.primaryColor
                                        .withValues(alpha: 0.12),
                                    widget.character.secondaryColor
                                        .withValues(alpha: 0.08),
                                    widget.character.primaryColor
                                        .withValues(alpha: 0.04),
                                  ],
                                ),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: widget.isSelected
                                ? Colors.white.withValues(alpha: 0.6)
                                : widget.character.primaryColor
                                    .withValues(alpha: 0.4),
                            width: 2,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: widget.character.primaryColor.withValues(
                                alpha: widget.isSelected ? 0.3 : 0.15,
                              ),
                              blurRadius: widget.isSelected ? 12 : 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Text(
                          widget.character.title,
                          style: TextStyle(
                            color: widget.isSelected
                                ? Colors.white.withValues(alpha: 0.95)
                                : widget.character.primaryColor
                                    .withValues(alpha: 0.9),
                            fontSize: subtitleFontSize,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.3,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // مؤشر الاختيار المحسن
                      if (widget.isSelected)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.white.withValues(alpha: 0.3),
                                Colors.white.withValues(alpha: 0.1),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.5),
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.white.withValues(alpha: 0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Text(
                            'مختارة',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 12,
                                    ),
                          ),
                        )
                            .animate()
                            .fadeIn(duration: 300.ms)
                            .scale(duration: 300.ms)
                            .shimmer(
                              duration: 2000.ms,
                              color: Colors.white.withOpacity(0.5),
                            ),
                    ],
                  ),
                ),

                // تأثير الضغط
                if (_isHovered)
                  Container(
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(AppConstants.borderRadius),
                      color: Colors.white.withOpacity(0.1),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              widget.character.primaryColor.withOpacity(0.1),
              widget.character.secondaryColor.withOpacity(0.1),
              widget.character.primaryColor.withOpacity(0.05),
            ],
          ),
        ),
      ).animate(onPlay: (controller) => controller.repeat()).shimmer(
            duration: 3000.ms,
            color: Colors.white.withOpacity(0.1),
          ),
    );
  }

  void _onHoverStart() {
    setState(() {
      _isHovered = true;
    });
    _hoverController.forward();
  }

  void _onHoverEnd() {
    setState(() {
      _isHovered = false;
    });
    _hoverController.reverse();
  }
}
