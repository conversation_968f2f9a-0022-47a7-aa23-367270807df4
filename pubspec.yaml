name: al_aql_al_mobde
description: العقل المبدع - تطبيق دردشة AI متعددة الشخصيات

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Animation
  cupertino_icons: ^1.0.2
  flutter_animate: ^4.2.0+1
  lottie: ^2.7.0
  shimmer: ^3.0.0
  
  # State Management
  provider: ^6.1.1
  
  # Database & Storage
  sqflite: ^2.3.0
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1
  
  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.2
  
  # Utilities
  intl: ^0.18.1
  uuid: ^4.1.0
  
  # Arabic & RTL Support
  flutter_localizations:
    sdk: flutter
  
  # Fonts & Icons
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  
  # Animations & Effects
  animated_text_kit: ^4.2.2
  particle_field: ^1.0.0
  
  # Chat Features
  flutter_chat_bubble: ^2.0.2
  
  # Permissions
  permission_handler: ^11.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/

  # Fonts
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
    - family: Vazirmatn
      fonts:
        - asset: assets/fonts/Vazirmatn-Regular.ttf
        - asset: assets/fonts/Vazirmatn-Bold.ttf
          weight: 700
        - asset: assets/fonts/Vazirmatn-SemiBold.ttf
          weight: 600

# Flutter build configuration
flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
    background_color: "#c039ff"
    theme_color: "#c039ff"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48
