import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../providers/chat_provider.dart';
import '../providers/character_provider.dart';
import '../models/ai_character.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';
import '../widgets/animated_background.dart';
import '../widgets/chat_message_bubble.dart';
import '../widgets/chat_input_field.dart';
import '../widgets/typing_indicator.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late ScrollController _scrollController;
  final TextEditingController _messageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadChatData();
  }

  void _initializeControllers() {
    _backgroundController = AnimationController(
      duration: AppConstants.backgroundAnimationDuration,
      vsync: this,
    );
    _scrollController = ScrollController();

    // بدء حركة الخلفية
    _backgroundController.repeat();
  }

  void _loadChatData() async {
    final chatProvider = context.read<ChatProvider>();
    final characterProvider = context.read<CharacterProvider>();

    // تحميل المحادثة الحالية
    if (characterProvider.selectedCharacter != null) {
      await chatProvider.loadConversation(
        characterProvider.selectedCharacter!.id,
      );
    }
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _scrollController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ChatProvider, CharacterProvider>(
      builder: (context, chatProvider, characterProvider, child) {
        final character = characterProvider.selectedCharacter;

        if (character == null) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        return Scaffold(
          body: Stack(
            children: [
              // الخلفية المتحركة مع ألوان الشخصية
              _buildCharacterBackground(character),

              // المحتوى الرئيسي
              SafeArea(
                child: Column(
                  children: [
                    // شريط العلوي
                    _buildAppBar(character),

                    // منطقة الرسائل
                    Expanded(
                      child: _buildMessagesArea(chatProvider, character),
                    ),

                    // مؤشر الكتابة
                    if (chatProvider.isTyping)
                      const TypingIndicator()
                          .animate()
                          .fadeIn(duration: 300.ms)
                          .slideY(begin: 20, duration: 300.ms),

                    // حقل إدخال الرسالة
                    ChatInputField(
                      controller: _messageController,
                      character: character,
                      onSendMessage: _sendMessage,
                      isLoading: chatProvider.isLoading,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCharacterBackground(AICharacter character) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.backgroundDark,
            character.primaryColor.withOpacity(0.1),
            character.secondaryColor.withOpacity(0.05),
          ],
        ),
      ),
      child: AnimatedBackground(controller: _backgroundController),
    );
  }

  Widget _buildAppBar(AICharacter character) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            character.primaryColor.withOpacity(0.1),
            Colors.transparent,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Row(
        children: [
          // زر الرجوع
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
            ),
          ),

          // معلومات الشخصية
          Expanded(
            child: Row(
              children: [
                // صورة الشخصية - تصميم حديث
                Container(
                  width: 55,
                  height: 55,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        character.primaryColor,
                        character.secondaryColor,
                      ],
                    ),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.4),
                      width: 2.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: character.primaryColor.withValues(alpha: 0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      character.emoji,
                      style: const TextStyle(
                        fontSize: 26,
                        shadows: [
                          Shadow(
                            color: Colors.black26,
                            offset: Offset(0, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // اسم ولقب الشخصية
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        character.name,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      Text(
                        character.title,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withOpacity(0.8),
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // زر الخيارات
          IconButton(
            onPressed: _showChatOptions,
            icon: const Icon(
              Icons.more_vert,
              color: Colors.white,
            ),
          ),
        ],
      ),
    )
        .animate()
        .slideY(begin: -50, duration: 600.ms, curve: Curves.easeOutCubic)
        .fadeIn(duration: 600.ms);
  }

  Widget _buildMessagesArea(ChatProvider chatProvider, AICharacter character) {
    if (chatProvider.messages.isEmpty) {
      return _buildEmptyState(character);
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: chatProvider.messages.length,
      itemBuilder: (context, index) {
        final message = chatProvider.messages[index];
        return ChatMessageBubble(
          message: message,
          character: character,
        )
            .animate()
            .slideY(
              begin: 50,
              duration: 400.ms,
              curve: Curves.easeOutCubic,
            )
            .fadeIn(duration: 400.ms);
      },
    );
  }

  Widget _buildEmptyState(AICharacter character) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: character.gradient,
              boxShadow: [
                BoxShadow(
                  color: character.primaryColor.withOpacity(0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Center(
              child: Text(
                character.emoji,
                style: const TextStyle(fontSize: 50),
              ),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            character.greeting,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            AppConstants.startChatPlaceholder,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withOpacity(0.7),
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    )
        .animate()
        .scale(duration: 800.ms, curve: Curves.elasticOut)
        .fadeIn(duration: 600.ms);
  }

  void _sendMessage(String content) async {
    if (content.trim().isEmpty) return;

    final chatProvider = context.read<ChatProvider>();
    final characterProvider = context.read<CharacterProvider>();

    if (characterProvider.selectedCharacter == null) return;

    // إرسال الرسالة
    await chatProvider.sendMessage(
      content,
      characterProvider.selectedCharacter!,
    );

    // مسح حقل الإدخال
    _messageController.clear();

    // التمرير للأسفل
    _scrollToBottom();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: AppConstants.shortAnimationDuration,
        curve: Curves.easeOutCubic,
      );
    }
  }

  void _showChatOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.delete_outline, color: Colors.white),
              title: const Text(
                'مسح المحادثة',
                style: TextStyle(color: Colors.white),
              ),
              onTap: () {
                Navigator.pop(context);
                _clearChat();
              },
            ),
            ListTile(
              leading: const Icon(Icons.person_outline, color: Colors.white),
              title: const Text(
                'تغيير الشخصية',
                style: TextStyle(color: Colors.white),
              ),
              onTap: () {
                Navigator.pop(context);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _clearChat() async {
    final chatProvider = context.read<ChatProvider>();
    await chatProvider.clearChat();
  }
}
