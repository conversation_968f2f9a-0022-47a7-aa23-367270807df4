import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/conversation.dart';
import '../models/chat_message.dart';
import '../utils/app_constants.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  Database? _database;
  bool _isInitialized = false;

  // Initialize the database
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kIsWeb) {
        // For web platform, we'll use SharedPreferences as fallback
        debugPrint('Running on web - using SharedPreferences for storage');
        _isInitialized = true;
        return;
      }

      final databasePath = await getDatabasesPath();
      final path = join(databasePath, AppConstants.databaseName);

      _database = await openDatabase(
        path,
        version: AppConstants.databaseVersion,
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
      );

      _isInitialized = true;
      debugPrint('Database initialized successfully');
    } catch (e) {
      debugPrint('Error initializing database: $e');
      // Don't rethrow - allow app to continue with limited functionality
      _isInitialized = true;
    }
  }

  // Create database tables
  Future<void> _createDatabase(Database db, int version) async {
    try {
      // Create conversations table
      await db.execute('''
        CREATE TABLE conversations (
          id TEXT PRIMARY KEY,
          character_id TEXT NOT NULL,
          title TEXT NOT NULL,
          created_at TEXT NOT NULL,
          last_message_at TEXT NOT NULL,
          metadata TEXT
        )
      ''');

      // Create messages table
      await db.execute('''
        CREATE TABLE messages (
          id TEXT PRIMARY KEY,
          conversation_id TEXT NOT NULL,
          content TEXT NOT NULL,
          type TEXT NOT NULL,
          timestamp TEXT NOT NULL,
          character_id TEXT NOT NULL,
          is_typing INTEGER DEFAULT 0,
          metadata TEXT,
          FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
        )
      ''');

      // Create indexes for better performance
      await db.execute(
          'CREATE INDEX idx_conversations_character_id ON conversations(character_id)');
      await db.execute(
          'CREATE INDEX idx_conversations_last_message_at ON conversations(last_message_at)');
      await db.execute(
          'CREATE INDEX idx_messages_conversation_id ON messages(conversation_id)');
      await db.execute(
          'CREATE INDEX idx_messages_timestamp ON messages(timestamp)');

      debugPrint('Database tables created successfully');
    } catch (e) {
      debugPrint('Error creating database tables: $e');
      rethrow;
    }
  }

  // Upgrade database schema
  Future<void> _upgradeDatabase(
      Database db, int oldVersion, int newVersion) async {
    try {
      // Handle database upgrades here
      debugPrint('Database upgraded from version $oldVersion to $newVersion');
    } catch (e) {
      debugPrint('Error upgrading database: $e');
      rethrow;
    }
  }

  // Get database instance
  Future<Database> get database async {
    if (!_isInitialized) {
      await initialize();
    }
    return _database!;
  }

  // Save conversation
  Future<void> saveConversation(ChatConversation conversation) async {
    try {
      if (kIsWeb) {
        // For web, use SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        final conversationJson = jsonEncode(conversation.toJson());
        await prefs.setString(
            'conversation_${conversation.id}', conversationJson);
        debugPrint(
            'Conversation saved to SharedPreferences: ${conversation.id}');
        return;
      }

      final db = await database;

      // Save conversation
      await db.insert(
        'conversations',
        {
          'id': conversation.id,
          'character_id': conversation.characterId,
          'title': conversation.title,
          'created_at': conversation.createdAt.toIso8601String(),
          'last_message_at': conversation.lastMessageAt.toIso8601String(),
          'metadata': conversation.metadata != null
              ? jsonEncode(conversation.metadata)
              : null,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      // Save messages
      for (final message in conversation.messages) {
        await _saveMessage(message, conversation.id);
      }

      debugPrint('Conversation saved: ${conversation.id}');
    } catch (e) {
      debugPrint('Error saving conversation: $e');
      rethrow;
    }
  }

  // Save individual message
  Future<void> _saveMessage(ChatMessage message, String conversationId) async {
    try {
      final db = await database;

      await db.insert(
        'messages',
        {
          'id': message.id,
          'conversation_id': conversationId,
          'content': message.content,
          'type': message.type.name,
          'timestamp': message.timestamp.toIso8601String(),
          'character_id': message.characterId,
          'is_typing': message.isTyping ? 1 : 0,
          'metadata':
              message.metadata != null ? jsonEncode(message.metadata) : null,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      debugPrint('Error saving message: $e');
      rethrow;
    }
  }

  // Get conversation by ID
  Future<ChatConversation?> getConversation(String conversationId) async {
    try {
      if (kIsWeb) {
        // For web, use SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        final conversationJson =
            prefs.getString('conversation_$conversationId');
        if (conversationJson != null) {
          final conversationData = jsonDecode(conversationJson);
          return ChatConversation.fromJson(conversationData);
        }
        return null;
      }

      final db = await database;

      // Get conversation
      final conversationResult = await db.query(
        'conversations',
        where: 'id = ?',
        whereArgs: [conversationId],
      );

      if (conversationResult.isEmpty) return null;

      final conversationData = conversationResult.first;

      // Get messages
      final messagesResult = await db.query(
        'messages',
        where: 'conversation_id = ?',
        whereArgs: [conversationId],
        orderBy: 'timestamp ASC',
      );

      final messages = messagesResult.map((messageData) {
        return ChatMessage.fromJson({
          'id': messageData['id'],
          'content': messageData['content'],
          'type': messageData['type'],
          'timestamp': messageData['timestamp'],
          'characterId': messageData['character_id'],
          'isTyping': messageData['is_typing'] == 1,
          'metadata': messageData['metadata'] != null
              ? jsonDecode(messageData['metadata'] as String)
              : null,
        });
      }).toList();

      return ChatConversation(
        id: conversationData['id'] as String,
        characterId: conversationData['character_id'] as String,
        title: conversationData['title'] as String,
        createdAt: DateTime.parse(conversationData['created_at'] as String),
        lastMessageAt:
            DateTime.parse(conversationData['last_message_at'] as String),
        messages: messages,
        metadata: conversationData['metadata'] != null
            ? jsonDecode(conversationData['metadata'] as String)
            : null,
      );
    } catch (e) {
      debugPrint('Error getting conversation: $e');
      return null;
    }
  }

  // Get all conversations
  Future<List<ChatConversation>> getAllConversations() async {
    try {
      if (kIsWeb) {
        // For web, get all conversations from SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        final keys =
            prefs.getKeys().where((key) => key.startsWith('conversation_'));
        final conversations = <ChatConversation>[];

        for (final key in keys) {
          final conversationJson = prefs.getString(key);
          if (conversationJson != null) {
            final conversationData = jsonDecode(conversationJson);
            conversations.add(ChatConversation.fromJson(conversationData));
          }
        }

        // Sort by last message time
        conversations
            .sort((a, b) => b.lastMessageAt.compareTo(a.lastMessageAt));
        return conversations;
      }

      final db = await database;

      final result = await db.query(
        'conversations',
        orderBy: 'last_message_at DESC',
      );

      final conversations = <ChatConversation>[];

      for (final conversationData in result) {
        final conversation =
            await getConversation(conversationData['id'] as String);
        if (conversation != null) {
          conversations.add(conversation);
        }
      }

      return conversations;
    } catch (e) {
      debugPrint('Error getting all conversations: $e');
      return [];
    }
  }

  // Get conversations by character
  Future<List<ChatConversation>> getConversationsByCharacter(
      String characterId) async {
    try {
      if (kIsWeb) {
        // For web, filter conversations by character from SharedPreferences
        final allConversations = await getAllConversations();
        return allConversations
            .where((conv) => conv.characterId == characterId)
            .toList();
      }

      final db = await database;

      final result = await db.query(
        'conversations',
        where: 'character_id = ?',
        whereArgs: [characterId],
        orderBy: 'last_message_at DESC',
      );

      final conversations = <ChatConversation>[];

      for (final conversationData in result) {
        final conversation =
            await getConversation(conversationData['id'] as String);
        if (conversation != null) {
          conversations.add(conversation);
        }
      }

      return conversations;
    } catch (e) {
      debugPrint('Error getting conversations by character: $e');
      return [];
    }
  }

  // Delete conversation
  Future<void> deleteConversation(String conversationId) async {
    try {
      final db = await database;

      // Delete messages first (due to foreign key constraint)
      await db.delete(
        'messages',
        where: 'conversation_id = ?',
        whereArgs: [conversationId],
      );

      // Delete conversation
      await db.delete(
        'conversations',
        where: 'id = ?',
        whereArgs: [conversationId],
      );

      debugPrint('Conversation deleted: $conversationId');
    } catch (e) {
      debugPrint('Error deleting conversation: $e');
      rethrow;
    }
  }

  // Delete all conversations for a character
  Future<void> deleteConversationsByCharacter(String characterId) async {
    try {
      final db = await database;

      // Get conversation IDs first
      final conversations = await db.query(
        'conversations',
        columns: ['id'],
        where: 'character_id = ?',
        whereArgs: [characterId],
      );

      // Delete messages for each conversation
      for (final conversation in conversations) {
        await db.delete(
          'messages',
          where: 'conversation_id = ?',
          whereArgs: [conversation['id']],
        );
      }

      // Delete conversations
      await db.delete(
        'conversations',
        where: 'character_id = ?',
        whereArgs: [characterId],
      );

      debugPrint('All conversations deleted for character: $characterId');
    } catch (e) {
      debugPrint('Error deleting conversations by character: $e');
      rethrow;
    }
  }

  // Clear all data
  Future<void> clearAllData() async {
    try {
      final db = await database;

      await db.delete('messages');
      await db.delete('conversations');

      debugPrint('All data cleared');
    } catch (e) {
      debugPrint('Error clearing all data: $e');
      rethrow;
    }
  }

  // Get database statistics
  Future<Map<String, int>> getDatabaseStats() async {
    try {
      final db = await database;

      final conversationCount = Sqflite.firstIntValue(
            await db.rawQuery('SELECT COUNT(*) FROM conversations'),
          ) ??
          0;

      final messageCount = Sqflite.firstIntValue(
            await db.rawQuery('SELECT COUNT(*) FROM messages'),
          ) ??
          0;

      return {
        'conversations': conversationCount,
        'messages': messageCount,
      };
    } catch (e) {
      debugPrint('Error getting database stats: $e');
      return {'conversations': 0, 'messages': 0};
    }
  }

  // Close database
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      _isInitialized = false;
      debugPrint('Database closed');
    }
  }
}
