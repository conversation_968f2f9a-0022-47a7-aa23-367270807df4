import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/ai_character.dart';
import '../models/chat_message.dart';

class ChatService {
  static final ChatService _instance = ChatService._internal();
  factory ChatService() => _instance;
  ChatService._internal();

  final Random _random = Random();

  // Generate AI response based on character and conversation context
  Future<String> generateResponse(
    String userMessage,
    AICharacter character,
    List<ChatMessage> conversationHistory,
  ) async {
    try {
      // Simulate network delay
      await Future.delayed(Duration(milliseconds: 1000 + _random.nextInt(2000)));

      // Get character-specific response
      return _generateCharacterResponse(userMessage, character, conversationHistory);
    } catch (e) {
      debugPrint('Error generating response: $e');
      return _getErrorResponse(character);
    }
  }

  // Generate character-specific response
  String _generateCharacterResponse(
    String userMessage,
    AICharacter character,
    List<ChatMessage> history,
  ) {
    final lowercaseMessage = userMessage.toLowerCase();
    
    switch (character.id) {
      case 'creativity':
        return _generateCreativityResponse(lowercaseMessage, history);
      case 'education':
        return _generateEducationResponse(lowercaseMessage, history);
      case 'communication':
        return _generateCommunicationResponse(lowercaseMessage, history);
      case 'wellness':
        return _generateWellnessResponse(lowercaseMessage, history);
      default:
        return _getDefaultResponse(character);
    }
  }

  // Creativity character responses
  String _generateCreativityResponse(String message, List<ChatMessage> history) {
    final creativityResponses = [
      // Greeting responses
      if (message.contains('مرحبا') || message.contains('أهلا') || message.contains('السلام'))
        'أهلاً وسهلاً بك في عالم الإبداع! 🎨 دعني أكون ملهمك في رحلة الفن والجمال. ما الذي يشعل شرارة الإبداع في قلبك اليوم؟',
      
      // Idea generation
      if (message.contains('فكرة') || message.contains('إبداع') || message.contains('ابتكار'))
        'إليك فكرة إبداعية رائعة! 💡 ما رأيك في إنشاء لوحة فنية تجمع بين الألوان الدافئة والباردة لتعبر عن التوازن في الحياة؟ أو ربما كتابة قصة قصيرة عن شخص يكتشف موهبة مخفية؟ الإبداع لا حدود له!',
      
      // Story writing
      if (message.contains('قصة') || message.contains('كتابة') || message.contains('سرد'))
        'القصص هي نوافذ الروح! ✨ لنبدأ بشخصية مثيرة للاهتمام... تخيل شخصاً يجد رسالة قديمة في كتاب مستعمل، هذه الرسالة تغير مجرى حياته تماماً. ما الذي تحتويه هذه الرسالة؟ وكيف ستؤثر على بطل قصتنا؟',
      
      // Art inspiration
      if (message.contains('فن') || message.contains('رسم') || message.contains('لوحة'))
        'الفن هو لغة القلب التي تتحدث بلا كلمات! 🎭 جرب أن ترسم مشاعرك اليوم بالألوان... استخدم الأزرق للهدوء، والأحمر للشغف، والأصفر للفرح. دع الفرشاة تتراقص على القماش وتحكي قصتك الخاصة.',
      
      // Music and poetry
      if (message.contains('موسيقى') || message.contains('شعر') || message.contains('أغنية'))
        'الموسيقى والشعر توأمان يرقصان في سماء الإبداع! 🎵 اكتب بيتاً شعرياً يصف شعورك الآن، أو ألحن لحناً بسيطاً يعبر عن حالتك المزاجية. الإبداع يولد من أعماق المشاعر الصادقة.',
      
      // General encouragement
      'الإبداع يسكن في كل منا، ينتظر فقط اللحظة المناسبة ليتفجر كالنور! ✨ لا تخف من التجريب، فكل خطأ هو درس، وكل محاولة هي خطوة نحو الإتقان. ما الذي تريد أن نبدعه معاً؟',
    ];

    return creativityResponses[_random.nextInt(creativityResponses.length)];
  }

  // Education character responses
  String _generateEducationResponse(String message, List<ChatMessage> history) {
    final educationResponses = [
      // Greeting responses
      if (message.contains('مرحبا') || message.contains('أهلا') || message.contains('السلام'))
        'وعليكم السلام ومرحباً بك طالب العلم! 📚 أنا هنا لأكون معك في رحلة التعلم والاكتشاف. العلم نور يضيء طريق الحياة، فما الذي تود أن نتعلمه اليوم؟',
      
      // Learning requests
      if (message.contains('تعلم') || message.contains('اشرح') || message.contains('علم'))
        'التعلم رحلة ممتعة لا تنتهي! 🌟 دعني أشرح لك بطريقة بسيطة ومفهومة. أولاً، لنبدأ بالأساسيات ثم نتدرج للتفاصيل. تذكر أن كل خبير كان يوماً مبتدئاً، والمهم هو الاستمرار والصبر.',
      
      // Study help
      if (message.contains('دراسة') || message.contains('امتحان') || message.contains('مذاكرة'))
        'الدراسة الفعالة تحتاج لاستراتيجية ذكية! 📖 أنصحك بتقسيم المادة لأجزاء صغيرة، واستخدام تقنية البومودورو (25 دقيقة دراسة، 5 دقائق راحة). لا تنس أن تراجع ما تعلمته بانتظام، فالتكرار أم العلوم.',
      
      // Science explanations
      if (message.contains('علوم') || message.contains('فيزياء') || message.contains('كيمياء'))
        'العلوم هي مفتاح فهم العالم من حولنا! 🔬 دعني أبسط لك المفاهيم العلمية بأمثلة من الحياة اليومية. تخيل أن الذرات مثل قطع الليجو الصغيرة التي تتجمع لتكون كل شيء حولنا!',
      
      // Mathematics
      if (message.contains('رياضيات') || message.contains('حساب') || message.contains('أرقام'))
        'الرياضيات لغة الكون الجميلة! ➕ لا تخف منها، فهي مجرد أنماط منطقية. ابدأ بالأساسيات وتدرج، واستخدم أمثلة عملية من حياتك اليومية. كل مسألة رياضية هي لغز ممتع ينتظر الحل!',
      
      // General encouragement
      'العلم كالنور، كلما زاد انتشر وأضاء طريق الآخرين! 💡 لا تتردد في طرح الأسئلة، فالسؤال نصف العلم. وتذكر أن الفشل جزء من التعلم، فكل خطأ يقربك خطوة من الفهم الصحيح.',
    ];

    return educationResponses[_random.nextInt(educationResponses.length)];
  }

  // Communication character responses
  String _generateCommunicationResponse(String message, List<ChatMessage> history) {
    final communicationResponses = [
      // Greeting responses
      if (message.contains('مرحبا') || message.contains('أهلا') || message.contains('السلام'))
        'أهلاً وسهلاً بك! 💼 يسعدني أن أكون مستشارك في فن التواصل والعلاقات. التواصل الفعال هو جسر النجاح في الحياة، فكيف يمكنني مساعدتك اليوم؟',
      
      // Communication skills
      if (message.contains('تواصل') || message.contains('حوار') || message.contains('كلام'))
        'التواصل الفعال فن يمكن تعلمه! 🗣️ أهم مبادئه: الاستماع الفعال، التعبير الواضح، واحترام وجهات النظر المختلفة. ابدأ بالاستماع أكثر من الكلام، فالأذن الصاغية تفتح القلوب.',
      
      // Persuasion and influence
      if (message.contains('إقناع') || message.contains('تأثير') || message.contains('إقناع'))
        'الإقناع الحقيقي يأتي من القلب قبل العقل! 💭 استخدم الحقائق والمنطق، لكن لا تنس العاطفة والقصص الشخصية. اجعل الطرف الآخر يشعر بأنك تفهمه وتقدر وجهة نظره.',
      
      // Conflict resolution
      if (message.contains('خلاف') || message.contains('مشكلة') || message.contains('نزاع'))
        'حل النزاعات يحتاج لحكمة ودبلوماسية! 🤝 ابحث عن نقاط الاتفاق أولاً، ثم تعامل مع الخلافات بهدوء وموضوعية. تذكر أن الهدف هو الوصول لحل يرضي الجميع، وليس الانتصار.',
      
      // Social skills
      if (message.contains('اجتماعي') || message.contains('أصدقاء') || message.contains('علاقات'))
        'العلاقات الاجتماعية كالحديقة، تحتاج للرعاية والاهتمام! 🌱 كن صادقاً ومخلصاً، أظهر اهتماماً حقيقياً بالآخرين، وتذكر أن العطاء أجمل من الأخذ في بناء الصداقات.',
      
      // General advice
      'التواصل الناجح يبدأ بفهم الذات ثم فهم الآخرين! 🎯 طور مهاراتك في القراءة بين السطور، وتعلم لغة الجسد، واجعل كلماتك جسراً للتفاهم وليس حاجزاً للخلاف.',
    ];

    return communicationResponses[_random.nextInt(communicationResponses.length)];
  }

  // Wellness character responses
  String _generateWellnessResponse(String message, List<ChatMessage> history) {
    final wellnessResponses = [
      // Greeting responses
      if (message.contains('مرحبا') || message.contains('أهلا') || message.contains('السلام'))
        'مرحباً بك في واحة السلام والهدوء 🧘‍♀️ أنا هنا لأكون معك في رحلة الراحة النفسية والسكينة. كيف حالك اليوم؟ وكيف يمكنني أن أساعدك على الشعور بالهدوء والطمأنينة؟',
      
      // Stress and anxiety
      if (message.contains('توتر') || message.contains('قلق') || message.contains('ضغط'))
        'أفهم شعورك تماماً، والتوتر جزء طبيعي من الحياة 🌸 جرب تمرين التنفس العميق: استنشق لأربع ثوان، احبس النفس لأربع، ثم أخرج الهواء لأربع ثوان. كرر هذا وستشعر بالهدوء يتسلل إليك.',
      
      // Relaxation and meditation
      if (message.contains('استرخاء') || message.contains('تأمل') || message.contains('هدوء'))
        'التأمل والاسترخاء غذاء الروح 🕯️ اجلس في مكان هادئ، أغمض عينيك، وركز على صوت تنفسك. تخيل نفسك في مكان جميل ومريح، واتركي كل الأفكار السلبية تتبدد مع كل زفير.',
      
      // Self-care
      if (message.contains('نفسي') || message.contains('راحة') || message.contains('اهتمام'))
        'الاهتمام بالنفس ليس أنانية، بل ضرورة! 💆‍♀️ خصص وقتاً يومياً لنفسك، اقرأ كتاباً تحبه، استمع لموسيقى هادئة، أو امش في الطبيعة. أنت تستحق كل الحب والاهتمام.',
      
      // Positive thinking
      if (message.contains('إيجابي') || message.contains('تفاؤل') || message.contains('أمل'))
        'التفكير الإيجابي نور يضيء أحلك الأوقات! ☀️ ابدأ يومك بثلاثة أشياء تشعر بالامتنان لوجودها في حياتك. تذكر أن كل تحدٍ هو فرصة للنمو، وكل صباح هو بداية جديدة مليئة بالإمكانيات.',
      
      // Sleep and rest
      if (message.contains('نوم') || message.contains('أرق') || message.contains('راحة'))
        'النوم الجيد أساس الصحة النفسية والجسدية 🌙 أنشئ روتيناً مسائياً مريحاً، تجنب الشاشات قبل النوم بساعة، واشرب شاياً دافئاً. اجعل غرفة نومك واحة هدوء وسكينة.',
      
      // General support
      'تذكر أنك أقوى مما تتخيل، وكل عاصفة تمر ستجعلك أكثر حكمة 🌈 أنا هنا دائماً لأستمع إليك وأدعمك. لا تتردد في مشاركة ما يثقل قلبك، فالحديث شفاء للروح.',
    ];

    return wellnessResponses[_random.nextInt(wellnessResponses.length)];
  }

  // Get default response for unknown character
  String _getDefaultResponse(AICharacter character) {
    return 'شكراً لك على رسالتك! أنا ${character.name} وأسعد دائماً بالحديث معك. كيف يمكنني مساعدتك اليوم؟ 😊';
  }

  // Get error response
  String _getErrorResponse(AICharacter character) {
    final errorResponses = [
      'أعتذر، حدث خطأ تقني بسيط. دعني أحاول مرة أخرى... 🔄',
      'يبدو أن هناك مشكلة في الاتصال. هل يمكنك إعادة المحاولة؟ 📡',
      'أواجه صعوبة في الرد الآن، لكنني هنا ومستعد للمساعدة! 💪',
    ];
    
    return errorResponses[_random.nextInt(errorResponses.length)];
  }

  // Get quick suggestions based on character and conversation
  List<String> getQuickSuggestions(AICharacter character, List<ChatMessage> history) {
    switch (character.id) {
      case 'creativity':
        return [
          'أريد فكرة إبداعية جديدة',
          'ساعدني في كتابة قصة',
          'أحتاج إلهام فني',
          'كيف أطور موهبتي؟',
        ];
      case 'education':
        return [
          'اشرح لي موضوعاً علمياً',
          'أريد تعلم شيء جديد',
          'ساعدني في الدراسة',
          'كيف أحسن طريقة تعلمي؟',
        ];
      case 'communication':
        return [
          'كيف أتواصل بفعالية؟',
          'أحتاج نصائح للحوار',
          'ساعدني في الإقناع',
          'كيف أحل النزاعات؟',
        ];
      case 'wellness':
        return [
          'أحتاج للاسترخاء',
          'كيف أتعامل مع التوتر؟',
          'أريد السلام النفسي',
          'ساعدني في التأمل',
        ];
      default:
        return [
          'مرحباً',
          'كيف حالك؟',
          'أحتاج مساعدة',
          'شكراً لك',
        ];
    }
  }
}
