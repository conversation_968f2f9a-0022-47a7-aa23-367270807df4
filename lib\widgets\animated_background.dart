import 'dart:math';
import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';

class AnimatedBackground extends StatefulWidget {
  final AnimationController controller;

  const AnimatedBackground({super.key, required this.controller});

  @override
  State<AnimatedBackground> createState() => _AnimatedBackgroundState();
}

class _AnimatedBackgroundState extends State<AnimatedBackground> {
  late List<Particle> particles;
  final Random random = Random();

  @override
  void initState() {
    super.initState();
    _initializeParticles();
  }

  void _initializeParticles() {
    particles = List.generate(AppConstants.particleCount, (index) {
      return Particle(
        x: random.nextDouble(),
        y: random.nextDouble(),
        size: random.nextDouble() * AppConstants.particleSize + 1,
        speed: random.nextDouble() * 0.02 + 0.01,
        opacity: random.nextDouble() * AppConstants.particleOpacity + 0.1,
        color: _getRandomParticleColor(),
      );
    });
  }

  Color _getRandomParticleColor() {
    final colors = [
      AppColors.primary,
      AppColors.secondary,
      AppColors.accent,
      Colors.white,
    ];
    return colors[random.nextInt(colors.length)];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF0A0A0A),
            const Color(0xFF1A1A2E),
            const Color(0xFF16213E),
            AppColors.primary.withValues(alpha: 0.15),
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
      ),
      child: Stack(
        children: [
          // الخلفية المتدرجة المتحركة
          AnimatedBuilder(
            animation: widget.controller,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF0A0A0A),
                      AppColors.primary.withValues(
                        alpha:
                            0.12 +
                            (sin(widget.controller.value * 2 * pi) * 0.08),
                      ),
                      AppColors.secondary.withValues(
                        alpha:
                            0.08 +
                            (cos(widget.controller.value * 2 * pi) * 0.06),
                      ),
                      const Color(0xFF16213E),
                    ],
                    stops: [
                      0.0,
                      0.4 + (sin(widget.controller.value * pi) * 0.15),
                      0.7 + (cos(widget.controller.value * pi * 0.8) * 0.1),
                      1.0,
                    ],
                  ),
                ),
              );
            },
          ),

          // الجسيمات المتحركة
          AnimatedBuilder(
            animation: widget.controller,
            builder: (context, child) {
              return CustomPaint(
                painter: ParticlePainter(
                  particles: particles,
                  animationValue: widget.controller.value,
                ),
                size: Size.infinite,
              );
            },
          ),

          // تأثير الضوء المتحرك
          AnimatedBuilder(
            animation: widget.controller,
            builder: (context, child) {
              return Positioned(
                left:
                    MediaQuery.of(context).size.width *
                    (0.5 + sin(widget.controller.value * 2 * pi) * 0.3),
                top:
                    MediaQuery.of(context).size.height *
                    (0.3 + cos(widget.controller.value * pi) * 0.2),
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        AppColors.accent.withOpacity(0.1),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              );
            },
          ),

          // تأثير ضوئي إضافي
          AnimatedBuilder(
            animation: widget.controller,
            builder: (context, child) {
              return Positioned(
                right:
                    MediaQuery.of(context).size.width *
                    (0.2 + cos(widget.controller.value * 1.5 * pi) * 0.2),
                bottom:
                    MediaQuery.of(context).size.height *
                    (0.4 + sin(widget.controller.value * 1.5 * pi) * 0.3),
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        AppColors.secondary.withOpacity(0.08),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class Particle {
  double x;
  double y;
  final double size;
  final double speed;
  final double opacity;
  final Color color;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.speed,
    required this.opacity,
    required this.color,
  });

  void update() {
    y -= speed;
    if (y < 0) {
      y = 1.0;
      x = Random().nextDouble();
    }
  }
}

class ParticlePainter extends CustomPainter {
  final List<Particle> particles;
  final double animationValue;

  ParticlePainter({required this.particles, required this.animationValue});

  @override
  void paint(Canvas canvas, Size size) {
    for (final particle in particles) {
      // تحديث موقع الجسيم
      particle.update();

      // رسم الجسيم
      final paint =
          Paint()
            ..color = particle.color.withOpacity(
              particle.opacity * (0.5 + sin(animationValue * 2 * pi) * 0.5),
            )
            ..style = PaintingStyle.fill;

      final center = Offset(particle.x * size.width, particle.y * size.height);

      // رسم دائرة مع تأثير التوهج
      canvas.drawCircle(center, particle.size, paint);

      // تأثير التوهج
      final glowPaint =
          Paint()
            ..color = particle.color.withOpacity(particle.opacity * 0.3)
            ..style = PaintingStyle.fill
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);

      canvas.drawCircle(center, particle.size * 1.5, glowPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
