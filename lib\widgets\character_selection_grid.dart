import 'package:flutter/material.dart';
import '../models/ai_character.dart';
import '../utils/app_constants.dart';
import 'character_card.dart';

class CharacterSelectionGrid extends StatefulWidget {
  final Function(String) onCharacterSelected;

  const CharacterSelectionGrid({
    super.key,
    required this.onCharacterSelected,
  });

  @override
  State<CharacterSelectionGrid> createState() => _CharacterSelectionGridState();
}

class _CharacterSelectionGridState extends State<CharacterSelectionGrid>
    with TickerProviderStateMixin {
  late List<AnimationController> _cardControllers;
  String? _selectedCharacterId;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _cardControllers = List.generate(
      AICharacters.allCharacters.length,
      (index) => AnimationController(
        duration: AppConstants.mediumAnimationDuration,
        vsync: this,
      ),
    );

    // بدء الحركات بتأخير متدرج
    for (int i = 0; i < _cardControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 150), () {
        if (mounted) {
          _cardControllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _cardControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 400;
    final crossAxisCount = isSmallScreen ? 1 : 2;
    final childAspectRatio = isSmallScreen ? 1.2 : 0.85;
    final padding =
        isSmallScreen ? AppConstants.paddingSmall : AppConstants.paddingMedium;

    return Padding(
      padding: EdgeInsets.all(padding),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: padding,
          mainAxisSpacing: padding,
          childAspectRatio: childAspectRatio,
        ),
        itemCount: AICharacters.allCharacters.length,
        itemBuilder: (context, index) {
          final character = AICharacters.allCharacters[index];
          final isSelected = _selectedCharacterId == character.id;

          return AnimatedBuilder(
            animation: _cardControllers[index],
            builder: (context, child) {
              return Transform.scale(
                scale: _cardControllers[index].value,
                child: Opacity(
                  opacity: _cardControllers[index].value,
                  child: CharacterCard(
                    character: character,
                    isSelected: isSelected,
                    onTap: () => _onCharacterTap(character),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  void _onCharacterTap(AICharacter character) async {
    if (_selectedCharacterId == character.id) {
      // إذا كانت الشخصية مختارة بالفعل، انتقل للدردشة
      widget.onCharacterSelected(character.id);
      return;
    }

    setState(() {
      _selectedCharacterId = character.id;
    });

    // تأثير بصري عند الاختيار
    final index = AICharacters.allCharacters.indexOf(character);
    await _cardControllers[index].reverse();
    await _cardControllers[index].forward();

    // انتظار قليل ثم الانتقال للدردشة
    await Future.delayed(const Duration(milliseconds: 800));

    if (mounted) {
      widget.onCharacterSelected(character.id);
    }
  }
}
