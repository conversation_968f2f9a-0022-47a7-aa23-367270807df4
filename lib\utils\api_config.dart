class ApiConfig {
  // مفتاح API الخاص بـ Gemini
  // يجب استبدال هذا المفتاح بمفتاح حقيقي من Google AI Studio
  static const String geminiApiKey = 'YOUR_GEMINI_API_KEY_HERE';
  
  // رابط API الأساسي
  static const String geminiBaseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
  
  // إعدادات الطلب
  static const Map<String, dynamic> defaultGenerationConfig = {
    'temperature': 0.8,
    'topK': 40,
    'topP': 0.95,
    'maxOutputTokens': 1024,
  };
  
  // إعدادات الأمان
  static const List<Map<String, String>> defaultSafetySettings = [
    {
      'category': 'HARM_CATEGORY_HARASSMENT',
      'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
    },
    {
      'category': 'HARM_CATEGORY_HATE_SPEECH',
      'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
    },
    {
      'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
      'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
    },
    {
      'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
      'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
    }
  ];
  
  // التحقق من صحة مفتاح API
  static bool get isApiKeyValid {
    return geminiApiKey.isNotEmpty && 
           geminiApiKey != 'YOUR_GEMINI_API_KEY_HERE' &&
           geminiApiKey.startsWith('AIza');
  }
  
  // رسالة تنبيه عند عدم وجود مفتاح API صحيح
  static const String apiKeyWarning = '''
تحذير: لم يتم تكوين مفتاح API الخاص بـ Gemini بشكل صحيح.

للحصول على مفتاح API:
1. اذهب إلى https://makersuite.google.com/app/apikey
2. قم بإنشاء مفتاح API جديد
3. استبدل القيمة في ملف lib/utils/api_config.dart

حالياً سيتم استخدام الردود المحلية بدلاً من Gemini AI.
''';
}
