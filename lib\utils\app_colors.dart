import 'package:flutter/material.dart';

class AppColors {
  // Modern Messenger-style colors
  static const Color primary = Color(0xFF0084FF);
  static const Color secondary = Color(0xFF7B68EE);
  static const Color primaryDark = Color(0xFF0A0A0A);
  static const Color secondaryDark = Color(0xFF1C1C1E);

  // Background colors - Dark modern theme
  static const Color background = Color(0xFF000000);
  static const Color backgroundDark = Color(0xFF0A0A0A);
  static const Color surface = Color(0xFF1C1C1E);
  static const Color surfaceLight = Color(0xFF2C2C2E);

  // Character colors - Premium luxury theme
  static const Color creativityPrimary = Color(0xFFE91E63);
  static const Color creativitySecondary = Color(0xFFFF6B35);

  static const Color educationPrimary = Color(0xFF4CAF50);
  static const Color educationSecondary = Color(0xFF8BC34A);

  static const Color communicationPrimary = Color(0xFF2196F3);
  static const Color communicationSecondary = Color(0xFF9C27B0);

  static const Color wellnessPrimary = Color(0xFF673AB7);
  static const Color wellnessSecondary = Color(0xFFE91E63);

  // Text colors
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFE0E6F0);
  static const Color textTertiary = Color(0xFFA0AEC0);

  // Message colors - Messenger style
  static const Color userMessageBackground = Color(0xFF0084FF);
  static const Color aiMessageBackground = Color(0xFF3A3A3C);

  // Accent colors
  static const Color accent = Color(0xFFFBBF24);
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);

  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, secondary],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondary, primary],
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF0A0A0A),
      Color(0xFF1A1A2E),
      Color(0xFF16213E),
      Color(0xFF0F3460),
      Color(0xFF533483)
    ],
    stops: [0.0, 0.25, 0.5, 0.75, 1.0],
  );

  static const LinearGradient creativityGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [creativityPrimary, creativitySecondary],
  );

  static const LinearGradient educationGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [educationPrimary, educationSecondary],
  );

  static const LinearGradient communicationGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [communicationPrimary, communicationSecondary],
  );

  static const LinearGradient wellnessGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [wellnessPrimary, wellnessSecondary],
  );

  // Glass morphism colors
  static Color glassMorphismBackground = Colors.white.withValues(alpha: 0.02);
  static Color glassMorphismBorder = Colors.white.withValues(alpha: 0.03);

  // Shadow colors
  static Color primaryShadow = primary.withValues(alpha: 0.3);
  static Color cardShadow = Colors.black.withValues(alpha: 0.1);

  // Particle colors for background effects
  static const List<Color> particleColors = [
    Color(0xFF93C5FD), // Blue
    Color(0xFFC4B5FD), // Purple
    Color(0xFFFCD34D), // Yellow
    Color(0xFFA7F3D0), // Green
    Color(0xFFFBBF24), // Orange
  ];

  // Character specific colors helper
  static LinearGradient getCharacterGradient(String characterId) {
    switch (characterId) {
      case 'creativity':
        return creativityGradient;
      case 'education':
        return educationGradient;
      case 'communication':
        return communicationGradient;
      case 'wellness':
        return wellnessGradient;
      default:
        return primaryGradient;
    }
  }

  static Color getCharacterPrimaryColor(String characterId) {
    switch (characterId) {
      case 'creativity':
        return creativityPrimary;
      case 'education':
        return educationPrimary;
      case 'communication':
        return communicationPrimary;
      case 'wellness':
        return wellnessPrimary;
      default:
        return primary;
    }
  }
}
