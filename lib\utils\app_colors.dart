import 'package:flutter/material.dart';

class AppColors {
  // Primary colors matching the original design
  static const Color primary = Color(0xFFC039FF);
  static const Color secondary = Color(0xFF6A11CB);
  static const Color primaryDark = Color(0xFF1A1A2E);
  static const Color secondaryDark = Color(0xFF16213E);

  // Background colors
  static const Color background = Color(0xFF0D1117);
  static const Color backgroundDark = Color(0xFF0A0E1A);
  static const Color surface = Color(0xFF1E1E2E);
  static const Color surfaceLight = Color(0xFF2A2A3E);

  // Character colors (matching original personalities)
  static const Color creativityPrimary = Color(0xFFFF6B6B);
  static const Color creativitySecondary = Color(0xFFEE5A24);

  static const Color educationPrimary = Color(0xFF4ECDC4);
  static const Color educationSecondary = Color(0xFF44A08D);

  static const Color communicationPrimary = Color(0xFFA29BFE);
  static const Color communicationSecondary = Color(0xFF6C5CE7);

  static const Color wellnessPrimary = Color(0xFFFD79A8);
  static const Color wellnessSecondary = Color(0xFFE84393);

  // Text colors
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFE0E6F0);
  static const Color textTertiary = Color(0xFFA0AEC0);

  // Message colors
  static const Color userMessageBackground = Color(0xFF667EEA);
  static const Color aiMessageBackground = Color(0xFF2A2A3E);

  // Accent colors
  static const Color accent = Color(0xFFFBBF24);
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);

  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, secondary],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondary, primary],
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryDark, secondaryDark, Color(0xFF0F3460), Color(0xFF533483)],
  );

  static const LinearGradient creativityGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [creativityPrimary, creativitySecondary],
  );

  static const LinearGradient educationGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [educationPrimary, educationSecondary],
  );

  static const LinearGradient communicationGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [communicationPrimary, communicationSecondary],
  );

  static const LinearGradient wellnessGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [wellnessPrimary, wellnessSecondary],
  );

  // Glass morphism colors
  static Color glassMorphismBackground = Colors.white.withOpacity(0.02);
  static Color glassMorphismBorder = Colors.white.withOpacity(0.03);

  // Shadow colors
  static Color primaryShadow = primary.withOpacity(0.3);
  static Color cardShadow = Colors.black.withOpacity(0.1);

  // Particle colors for background effects
  static const List<Color> particleColors = [
    Color(0xFF93C5FD), // Blue
    Color(0xFFC4B5FD), // Purple
    Color(0xFFFCD34D), // Yellow
    Color(0xFFA7F3D0), // Green
    Color(0xFFFBBF24), // Orange
  ];

  // Character specific colors helper
  static LinearGradient getCharacterGradient(String characterId) {
    switch (characterId) {
      case 'creativity':
        return creativityGradient;
      case 'education':
        return educationGradient;
      case 'communication':
        return communicationGradient;
      case 'wellness':
        return wellnessGradient;
      default:
        return primaryGradient;
    }
  }

  static Color getCharacterPrimaryColor(String characterId) {
    switch (characterId) {
      case 'creativity':
        return creativityPrimary;
      case 'education':
        return educationPrimary;
      case 'communication':
        return communicationPrimary;
      case 'wellness':
        return wellnessPrimary;
      default:
        return primary;
    }
  }
}
