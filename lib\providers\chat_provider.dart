import 'package:flutter/foundation.dart';
import '../models/chat_message.dart';
import '../models/conversation.dart';
import '../models/ai_character.dart';
import '../services/chat_service.dart';
import '../services/storage_service.dart';
import '../services/gemini_service.dart';
import '../utils/app_constants.dart';

class ChatProvider extends ChangeNotifier {
  final ChatService _chatService = ChatService();
  final StorageService _storageService = StorageService();

  List<ChatMessage> _messages = [];
  bool _isLoading = false;
  bool _isTyping = false;
  String? _currentCharacterId;
  String? _error;
  ChatConversation? _currentConversation;

  // Getters
  List<ChatMessage> get messages => _messages;
  bool get isLoading => _isLoading;
  bool get isTyping => _isTyping;
  String? get currentCharacterId => _currentCharacterId;
  String? get error => _error;
  ChatConversation? get currentConversation => _currentConversation;
  bool get hasMessages => _messages.isNotEmpty;
  int get messageCount => _messages.length;

  // Initialize chat provider
  Future<void> initialize() async {
    try {
      await _storageService.initialize();
    } catch (e) {
      debugPrint('Error initializing ChatProvider: $e');
      _error = 'فشل في تهيئة نظام الدردشة';
      notifyListeners();
    }
  }

  // Start new conversation with character
  Future<void> startConversation(AICharacter character) async {
    try {
      _isLoading = true;
      _error = null;
      _currentCharacterId = character.id;
      notifyListeners();

      // Clear previous messages
      _messages.clear();

      // Create new conversation
      _currentConversation = ChatConversation(
        characterId: character.id,
        title: 'محادثة مع ${character.name}',
      );

      // Add welcome message
      final welcomeMessage = ChatMessage(
        content: character.greeting,
        type: MessageType.ai,
        characterId: character.id,
      );

      _messages.add(welcomeMessage);
      _currentConversation = _currentConversation!.addMessage(welcomeMessage);

      // Save conversation
      await _storageService.saveConversation(_currentConversation!);

      debugPrint('Started conversation with ${character.name}');
    } catch (e) {
      debugPrint('Error starting conversation: $e');
      _error = 'فشل في بدء المحادثة';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Send message
  Future<void> sendMessage(String content, AICharacter character) async {
    if (content.trim().isEmpty) {
      _error = AppConstants.emptyMessageError;
      notifyListeners();
      return;
    }

    try {
      _error = null;

      // Add user message
      final userMessage = ChatMessage(
        content: content.trim(),
        type: MessageType.user,
        characterId: character.id,
      );

      _messages.add(userMessage);
      _currentConversation = _currentConversation?.addMessage(userMessage);
      notifyListeners();

      // Show typing indicator
      _isTyping = true;
      notifyListeners();

      // Get AI response using Gemini API
      final aiResponse = await GeminiService.generateResponse(
        message: content,
        character: character,
        conversationHistory: _messages,
      );

      // Hide typing indicator
      _isTyping = false;

      // Add AI message
      final aiMessage = ChatMessage(
        content: aiResponse,
        type: MessageType.ai,
        characterId: character.id,
      );

      _messages.add(aiMessage);
      _currentConversation = _currentConversation?.addMessage(aiMessage);

      // Save conversation
      if (_currentConversation != null) {
        await _storageService.saveConversation(_currentConversation!);
      }

      debugPrint('Message sent and response received');
    } catch (e) {
      debugPrint('Error sending message: $e');
      _error = 'فشل في إرسال الرسالة';
      _isTyping = false;
    } finally {
      notifyListeners();
    }
  }

  // Load conversation history
  Future<void> loadConversation(String conversationId) async {
    try {
      _isLoading = true;
      _error = null;
      // لا نستدعي notifyListeners هنا لتجنب setState during build

      final conversation =
          await _storageService.getConversation(conversationId);
      if (conversation != null) {
        _currentConversation = conversation;
        _messages = conversation.messages;
        _currentCharacterId = conversation.characterId;
      }

      debugPrint('Loaded conversation: $conversationId');
    } catch (e) {
      debugPrint('Error loading conversation: $e');
      _error = 'فشل في تحميل المحادثة';
    } finally {
      _isLoading = false;
      // لا نستدعي notifyListeners هنا لتجنب setState during build
    }
  }

  // Load conversation and notify listeners (safe for UI updates)
  Future<void> loadConversationAndNotify(String conversationId) async {
    await loadConversation(conversationId);
    notifyListeners();
  }

  // Load chat history for character
  Future<void> loadChatHistory(String characterId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final conversations =
          await _storageService.getConversationsByCharacter(characterId);
      if (conversations.isNotEmpty) {
        // Load the most recent conversation
        final latestConversation = conversations.first;
        _currentConversation = latestConversation;
        _messages = latestConversation.messages;
        _currentCharacterId = characterId;
      } else {
        _messages.clear();
        _currentConversation = null;
        _currentCharacterId = characterId;
      }

      debugPrint('Loaded chat history for character: $characterId');
    } catch (e) {
      debugPrint('Error loading chat history: $e');
      _error = 'فشل في تحميل تاريخ المحادثة';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear current chat
  Future<void> clearChat() async {
    try {
      _messages.clear();
      _currentConversation = null;
      _error = null;
      notifyListeners();

      debugPrint('Chat cleared');
    } catch (e) {
      debugPrint('Error clearing chat: $e');
      _error = 'فشل في مسح المحادثة';
      notifyListeners();
    }
  }

  // Delete conversation
  Future<void> deleteConversation(String conversationId) async {
    try {
      await _storageService.deleteConversation(conversationId);

      if (_currentConversation?.id == conversationId) {
        await clearChat();
      }

      debugPrint('Deleted conversation: $conversationId');
    } catch (e) {
      debugPrint('Error deleting conversation: $e');
      _error = 'فشل في حذف المحادثة';
      notifyListeners();
    }
  }

  // Get all conversations
  Future<List<ChatConversation>> getAllConversations() async {
    try {
      return await _storageService.getAllConversations();
    } catch (e) {
      debugPrint('Error getting all conversations: $e');
      return [];
    }
  }

  // Get conversations by character
  Future<List<ChatConversation>> getConversationsByCharacter(
      String characterId) async {
    try {
      return await _storageService.getConversationsByCharacter(characterId);
    } catch (e) {
      debugPrint('Error getting conversations by character: $e');
      return [];
    }
  }

  // Search messages
  List<ChatMessage> searchMessages(String query) {
    if (query.trim().isEmpty) return _messages;

    final lowercaseQuery = query.toLowerCase();
    return _messages.where((message) {
      return message.content.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Get message by ID
  ChatMessage? getMessageById(String messageId) {
    try {
      return _messages.firstWhere((message) => message.id == messageId);
    } catch (e) {
      return null;
    }
  }

  // Update message
  void updateMessage(String messageId, String newContent) {
    final messageIndex = _messages.indexWhere((m) => m.id == messageId);
    if (messageIndex != -1) {
      _messages[messageIndex] =
          _messages[messageIndex].copyWith(content: newContent);
      notifyListeners();
    }
  }

  // Delete message
  void deleteMessage(String messageId) {
    _messages.removeWhere((message) => message.id == messageId);
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Reset provider
  void reset() {
    _messages.clear();
    _currentConversation = null;
    _currentCharacterId = null;
    _isLoading = false;
    _isTyping = false;
    _error = null;
    notifyListeners();
  }

  // Get chat statistics
  Map<String, dynamic> getChatStatistics() {
    final userMessages =
        _messages.where((m) => m.type == MessageType.user).length;
    final aiMessages = _messages.where((m) => m.type == MessageType.ai).length;

    return {
      'totalMessages': _messages.length,
      'userMessages': userMessages,
      'aiMessages': aiMessages,
      'conversationStartTime': _currentConversation?.createdAt,
      'lastMessageTime': _messages.isNotEmpty ? _messages.last.timestamp : null,
    };
  }
}
