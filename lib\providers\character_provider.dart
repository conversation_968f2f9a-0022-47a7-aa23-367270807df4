import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/ai_character.dart';
import '../utils/app_constants.dart';

class CharacterProvider extends ChangeNotifier {
  AICharacter? _selectedCharacter;
  List<AICharacter> _characters = AICharacters.getAllCharacters();
  bool _isLoading = false;

  // Getters
  AICharacter? get selectedCharacter => _selectedCharacter;
  List<AICharacter> get characters => _characters;
  bool get isLoading => _isLoading;
  bool get hasSelectedCharacter => _selectedCharacter != null;

  // Initialize provider
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _loadSelectedCharacter();
    } catch (e) {
      debugPrint('Error initializing CharacterProvider: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load selected character from storage
  Future<void> _loadSelectedCharacter() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final characterId = prefs.getString(AppConstants.selectedCharacterKey);

      if (characterId != null) {
        _selectedCharacter = AICharacters.getCharacterById(characterId);
      }
    } catch (e) {
      debugPrint('Error loading selected character: $e');
    }
  }

  // Select a character
  Future<void> selectCharacter(AICharacter character) async {
    try {
      _selectedCharacter = character;
      notifyListeners();

      // Save to storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.selectedCharacterKey, character.id);

      debugPrint('Selected character: ${character.name}');
    } catch (e) {
      debugPrint('Error selecting character: $e');
    }
  }

  // Select character by ID
  Future<void> selectCharacterById(String characterId) async {
    final character = AICharacters.getCharacterById(characterId);
    if (character != null) {
      await selectCharacter(character);
    }
  }

  // Clear selected character
  Future<void> clearSelectedCharacter() async {
    try {
      _selectedCharacter = null;
      notifyListeners();

      // Remove from storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.selectedCharacterKey);

      debugPrint('Cleared selected character');
    } catch (e) {
      debugPrint('Error clearing selected character: $e');
    }
  }

  // Get character by ID
  AICharacter? getCharacterById(String id) {
    return AICharacters.getCharacterById(id);
  }

  // Get character suggestions
  List<String> getCharacterSuggestions([String? characterId]) {
    final id = characterId ?? _selectedCharacter?.id;
    if (id == null) return [];

    return AICharacters.getCharacterSuggestions(id);
  }

  // Check if character is selected
  bool isCharacterSelected(String characterId) {
    return _selectedCharacter?.id == characterId;
  }

  // Get next character (for navigation)
  AICharacter? getNextCharacter() {
    if (_selectedCharacter == null) return null;

    final currentIndex = _characters.indexWhere(
      (char) => char.id == _selectedCharacter!.id,
    );

    if (currentIndex == -1 || currentIndex == _characters.length - 1) {
      return _characters.first;
    }

    return _characters[currentIndex + 1];
  }

  // Get previous character (for navigation)
  AICharacter? getPreviousCharacter() {
    if (_selectedCharacter == null) return null;

    final currentIndex = _characters.indexWhere(
      (char) => char.id == _selectedCharacter!.id,
    );

    if (currentIndex == -1 || currentIndex == 0) {
      return _characters.last;
    }

    return _characters[currentIndex - 1];
  }

  // Get character statistics
  Map<String, dynamic> getCharacterStats(String characterId) {
    // This would typically come from a database
    // For now, return mock data
    return {
      'totalChats': 0,
      'totalMessages': 0,
      'lastChatDate': null,
      'averageResponseTime': 0,
      'userRating': 0.0,
    };
  }

  // Search characters
  List<AICharacter> searchCharacters(String query) {
    if (query.isEmpty) return _characters;

    final lowercaseQuery = query.toLowerCase();
    return _characters.where((character) {
      return character.name.toLowerCase().contains(lowercaseQuery) ||
          character.title.toLowerCase().contains(lowercaseQuery) ||
          character.personality.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Get character by personality trait
  List<AICharacter> getCharactersByTrait(String trait) {
    final lowercaseTrait = trait.toLowerCase();
    return _characters.where((character) {
      return character.personality.toLowerCase().contains(lowercaseTrait) ||
          character.style.toLowerCase().contains(lowercaseTrait);
    }).toList();
  }

  // Refresh characters (for future updates)
  Future<void> refreshCharacters() async {
    _isLoading = true;
    notifyListeners();

    try {
      // In a real app, this would fetch from an API
      await Future.delayed(const Duration(seconds: 1));
      _characters = AICharacters.getAllCharacters();

      // Revalidate selected character
      if (_selectedCharacter != null) {
        final updatedCharacter =
            AICharacters.getCharacterById(_selectedCharacter!.id);
        if (updatedCharacter != null) {
          _selectedCharacter = updatedCharacter;
        } else {
          _selectedCharacter = null;
        }
      }
    } catch (e) {
      debugPrint('Error refreshing characters: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Reset provider
  void reset() {
    _selectedCharacter = null;
    _characters = AICharacters.getAllCharacters();
    _isLoading = false;
    notifyListeners();
  }
}
